#!/usr/bin/env python3
"""
Test script to debug environment variable loading
"""
import os
import sys

# Add the member service directory to the path
sys.path.append('microservices/member-service')

print("🔍 Environment Variable Debug Test")
print("=" * 50)

# Test 1: Check environment variables directly
print("\n1. Direct Environment Variables:")
groq_key = os.environ.get('GROQ_API_KEY')
openai_key = os.environ.get('OPENAI_API_KEY')

print(f"   GROQ_API_KEY: {'✅ Set' if groq_key else '❌ Not set'}")
if groq_key:
    print(f"   GROQ_API_KEY value: {groq_key[:10]}...")

print(f"   OPENAI_API_KEY: {'✅ Set' if openai_key else '❌ Not set'}")
if openai_key:
    print(f"   OPENAI_API_KEY value: {openai_key[:10]}...")

# Test 2: Try loading .env file
print("\n2. Loading .env file:")
try:
    from dotenv import load_dotenv
    load_dotenv('microservices/member-service/.env')
    print("   ✅ .env file loaded successfully")
    
    # Check again after loading .env
    groq_key_after = os.environ.get('GROQ_API_KEY')
    openai_key_after = os.environ.get('OPENAI_API_KEY')
    
    print(f"   GROQ_API_KEY after .env: {'✅ Set' if groq_key_after else '❌ Not set'}")
    if groq_key_after:
        print(f"   GROQ_API_KEY value: {groq_key_after[:10]}...")
        
except ImportError:
    print("   ⚠️  python-dotenv not available")
except Exception as e:
    print(f"   ❌ Error loading .env: {e}")

# Test 3: Check OpenAI library availability
print("\n3. OpenAI Library Check:")
try:
    from openai import OpenAI
    print("   ✅ OpenAI library available")
except ImportError:
    print("   ❌ OpenAI library not available")

# Test 4: Test AI Chat Assistant initialization
print("\n4. AI Chat Assistant Initialization:")
try:
    from ai_chat_assistant import LibraryAIChatAssistant
    assistant = LibraryAIChatAssistant()
    
    print(f"   AI Provider: {assistant.ai_provider}")
    print(f"   AI Available: {assistant.ai_available}")
    print(f"   AI Model: {assistant.ai_model}")
    
    if hasattr(assistant, 'groq_api_key'):
        print(f"   Groq Key Detected: {'✅ Yes' if assistant.groq_api_key else '❌ No'}")
    if hasattr(assistant, 'openai_api_key'):
        print(f"   OpenAI Key Detected: {'✅ Yes' if assistant.openai_api_key else '❌ No'}")
        
except Exception as e:
    print(f"   ❌ Error initializing AI assistant: {e}")

print("\n" + "=" * 50)
print("🏁 Debug test completed!")
