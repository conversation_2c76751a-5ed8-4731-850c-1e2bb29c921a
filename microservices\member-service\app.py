from flask import Flask, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
from flask_jwt_extended import J<PERSON><PERSON>ana<PERSON>, decode_token
from datetime import datetime, timedelta
import os
import requests

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    import os
    # Load .env file from the current directory (member-service directory)
    env_path = os.path.join(os.path.dirname(__file__), '.env')
    load_dotenv(env_path)
    print(f"[MEMBER SERVICE] Loaded environment variables from {env_path}")

    # Debug: Check if GROQ_API_KEY is loaded
    groq_key = os.environ.get('GROQ_API_KEY')
    if groq_key:
        print(f"[MEMBER SERVICE] GROQ_API_KEY loaded: {groq_key[:10]}...")
    else:
        print("[MEMBER SERVICE] GROQ_API_KEY not found in environment")
except ImportError:
    print("[MEMBER SERVICE] python-dotenv not installed, using system environment variables")

app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'member-service-secret')
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get(
    'DATABASE_URI',
    'mysql+pymysql://root:@localhost:3306/biblioai_members'  # XAMPP MySQL configuration
)
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['AUTH_SERVICE_URL'] = os.environ.get('AUTH_SERVICE_URL', 'http://localhost:5001')
app.config['DOCUMENT_SERVICE_URL'] = os.environ.get('DOCUMENT_SERVICE_URL', 'http://localhost:5005')
app.config['NOTIFICATION_SERVICE_URL'] = os.environ.get('NOTIFICATION_SERVICE_URL', 'http://localhost:5007')
# JWT Configuration - Must match auth service
app.config['JWT_SECRET_KEY'] = os.environ.get('JWT_SECRET_KEY', 'biblioai-jwt-secret-key-2024')

# Initialize extensions
db = SQLAlchemy(app)
jwt = JWTManager(app)
CORS(app)

# Initialize AI Chat Assistant globally
try:
    from ai_chat_assistant import LibraryAIChatAssistant
    ai_assistant = LibraryAIChatAssistant()
    print(f"[MEMBER SERVICE] AI Assistant initialized: {ai_assistant.ai_provider} (available: {ai_assistant.ai_available})")
except Exception as e:
    print(f"[MEMBER SERVICE] Failed to initialize AI Assistant: {e}")
    ai_assistant = None

# Member Models
class Reservation(db.Model):
    __tablename__ = 'reservations'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, nullable=False)
    document_id = db.Column(db.Integer, nullable=False)
    status = db.Column(db.String(20), default='active')  # active, fulfilled, cancelled, expired
    reservation_date = db.Column(db.DateTime, default=datetime.utcnow)
    expiry_date = db.Column(db.DateTime, nullable=False)
    priority = db.Column(db.Integer, default=1)  # Queue position
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'document_id': self.document_id,
            'status': self.status,
            'reservation_date': self.reservation_date.isoformat() if self.reservation_date else None,
            'expiry_date': self.expiry_date.isoformat() if self.expiry_date else None,
            'priority': self.priority,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class Borrowing(db.Model):
    __tablename__ = 'borrowings'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, nullable=False)
    document_id = db.Column(db.Integer, nullable=False)
    status = db.Column(db.String(20), default='active')  # active, returned, overdue, lost
    borrow_date = db.Column(db.DateTime, default=datetime.utcnow)
    due_date = db.Column(db.DateTime, nullable=False)
    return_date = db.Column(db.DateTime)
    renewal_count = db.Column(db.Integer, default=0)
    max_renewals = db.Column(db.Integer, default=2)
    fine_amount = db.Column(db.Numeric(10, 2), default=0.00)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'document_id': self.document_id,
            'status': self.status,
            'borrow_date': self.borrow_date.isoformat() if self.borrow_date else None,
            'due_date': self.due_date.isoformat() if self.due_date else None,
            'return_date': self.return_date.isoformat() if self.return_date else None,
            'renewal_count': self.renewal_count,
            'max_renewals': self.max_renewals,
            'fine_amount': float(self.fine_amount) if self.fine_amount else 0.00,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class MemberHistory(db.Model):
    __tablename__ = 'member_history'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, nullable=False)
    document_id = db.Column(db.Integer, nullable=False)
    action = db.Column(db.String(50), nullable=False)  # reserved, borrowed, returned, renewed
    action_date = db.Column(db.DateTime, default=datetime.utcnow)
    details = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'document_id': self.document_id,
            'action': self.action,
            'action_date': self.action_date.isoformat() if self.action_date else None,
            'details': self.details,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

# Authentication middleware
def verify_token():
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return None
    
    token = auth_header.split(' ')[1]
    try:
        decoded_token = decode_token(token)
        return decoded_token
    except Exception as e:
        print(f"Token verification failed: {str(e)}")
        return None

def require_auth(f):
    def decorated_function(*args, **kwargs):
        token_data = verify_token()
        if not token_data:
            return jsonify({'message': 'Authentication required'}), 401
        
        request.user_id = int(token_data['sub'])
        request.user_role = token_data.get('role', 'member')
        return f(*args, **kwargs)
    
    decorated_function.__name__ = f.__name__
    return decorated_function

# Helper functions
def get_document_info(document_id):
    """Get document information from document service"""
    try:
        # Get the current request's authorization header
        auth_header = request.headers.get('Authorization') if request else None
        headers = {'Authorization': auth_header} if auth_header else {}

        response = requests.get(f"{app.config['DOCUMENT_SERVICE_URL']}/documents/{document_id}", headers=headers)
        if response.status_code == 200:
            return response.json()
        else:
            print(f"Document service returned {response.status_code}: {response.text}")
        return None
    except Exception as e:
        print(f"Error fetching document info: {str(e)}")
        return None

def update_document_availability(document_id, action):
    """Update document availability in document service"""
    try:
        # Get the current request's authorization header
        auth_header = request.headers.get('Authorization') if request else None
        headers = {'Authorization': auth_header} if auth_header else {}

        endpoint = f"{app.config['DOCUMENT_SERVICE_URL']}/documents/{document_id}/availability"
        data = {'action': action}  # 'borrow' or 'return'
        response = requests.put(endpoint, json=data, headers=headers)
        if response.status_code != 200:
            print(f"Document availability update failed: {response.status_code} - {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error updating document availability: {str(e)}")
        return False

def send_notification(user_id, notification_type, message, document_id=None):
    """Send notification via notification service"""
    try:
        data = {
            'user_id': user_id,
            'type': notification_type,
            'message': message,
            'document_id': document_id
        }
        # Note: Notification service doesn't require auth for internal calls
        response = requests.post(f"{app.config['NOTIFICATION_SERVICE_URL']}/notifications", json=data)
        if response.status_code != 201:
            print(f"Notification service returned {response.status_code}: {response.text}")
        return response.status_code == 201
    except Exception as e:
        print(f"Error sending notification: {str(e)}")
        return False

# Health check endpoint
@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({'status': 'healthy', 'service': 'member-service'}), 200

# Reservation endpoints
@app.route('/reservations', methods=['GET'])
@require_auth
def get_user_reservations():
    """Get user's reservations"""
    try:
        reservations = Reservation.query.filter_by(user_id=request.user_id).order_by(Reservation.created_at.desc()).all()
        
        # Enrich with document information
        enriched_reservations = []
        for reservation in reservations:
            reservation_dict = reservation.to_dict()
            document_info = get_document_info(reservation.document_id)
            if document_info:
                reservation_dict['document'] = document_info
            enriched_reservations.append(reservation_dict)
        
        return jsonify({
            'reservations': enriched_reservations,
            'total': len(enriched_reservations)
        }), 200
    except Exception as e:
        return jsonify({'message': f'Error fetching reservations: {str(e)}'}), 500

@app.route('/reservations', methods=['POST'])
@require_auth
def create_reservation():
    """Create a new reservation"""
    try:
        data = request.get_json()
        document_id = data.get('document_id')
        
        if not document_id:
            return jsonify({'message': 'Document ID is required'}), 400
        
        # Check if document exists and is available
        document_info = get_document_info(document_id)
        if not document_info:
            return jsonify({'message': 'Document not found'}), 404
        
        # Check if user already has an active reservation for this document
        existing_reservation = Reservation.query.filter_by(
            user_id=request.user_id,
            document_id=document_id,
            status='active'
        ).first()
        
        if existing_reservation:
            return jsonify({'message': 'You already have an active reservation for this document'}), 400
        
        # Check if user is already borrowing this document
        active_borrowing = Borrowing.query.filter_by(
            user_id=request.user_id,
            document_id=document_id,
            status='active'
        ).first()
        
        if active_borrowing:
            return jsonify({'message': 'You are already borrowing this document'}), 400
        
        # Calculate expiry date (7 days from now)
        expiry_date = datetime.utcnow() + timedelta(days=7)
        
        # Get next priority number for this document
        max_priority = db.session.query(db.func.max(Reservation.priority)).filter_by(
            document_id=document_id,
            status='active'
        ).scalar() or 0
        
        # Create reservation
        reservation = Reservation(
            user_id=request.user_id,
            document_id=document_id,
            expiry_date=expiry_date,
            priority=max_priority + 1,
            notes=data.get('notes', '')
        )
        
        db.session.add(reservation)
        
        # Add to history
        history = MemberHistory(
            user_id=request.user_id,
            document_id=document_id,
            action='reserved',
            details=f"Reserved document: {document_info.get('title', 'Unknown')}"
        )
        db.session.add(history)
        
        db.session.commit()
        
        # Send notification
        send_notification(
            request.user_id,
            'reservation_created',
            f"You have successfully reserved '{document_info.get('title', 'Unknown')}'",
            document_id
        )
        
        return jsonify({
            'message': 'Reservation created successfully',
            'reservation': reservation.to_dict()
        }), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Error creating reservation: {str(e)}'}), 500

@app.route('/reservations/<int:reservation_id>', methods=['DELETE'])
@require_auth
def cancel_reservation(reservation_id):
    """Cancel a reservation"""
    try:
        reservation = Reservation.query.filter_by(
            id=reservation_id,
            user_id=request.user_id,
            status='active'
        ).first()

        if not reservation:
            return jsonify({'message': 'Reservation not found or already cancelled'}), 404

        reservation.status = 'cancelled'
        reservation.updated_at = datetime.utcnow()

        # Add to history
        document_info = get_document_info(reservation.document_id)
        history = MemberHistory(
            user_id=request.user_id,
            document_id=reservation.document_id,
            action='cancelled',
            details=f"Cancelled reservation for: {document_info.get('title', 'Unknown') if document_info else 'Unknown'}"
        )
        db.session.add(history)

        db.session.commit()

        return jsonify({'message': 'Reservation cancelled successfully'}), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Error cancelling reservation: {str(e)}'}), 500

# Borrowing endpoints
@app.route('/borrowings', methods=['GET'])
@require_auth
def get_user_borrowings():
    """Get user's borrowings"""
    try:
        borrowings = Borrowing.query.filter_by(user_id=request.user_id).order_by(Borrowing.created_at.desc()).all()

        # Enrich with document information
        enriched_borrowings = []
        for borrowing in borrowings:
            borrowing_dict = borrowing.to_dict()
            document_info = get_document_info(borrowing.document_id)
            if document_info:
                borrowing_dict['document'] = document_info

            # Calculate days until due
            if borrowing.due_date and borrowing.status == 'active':
                days_until_due = (borrowing.due_date - datetime.utcnow()).days
                borrowing_dict['days_until_due'] = days_until_due
                borrowing_dict['is_overdue'] = days_until_due < 0

            enriched_borrowings.append(borrowing_dict)

        return jsonify({
            'borrowings': enriched_borrowings,
            'total': len(enriched_borrowings)
        }), 200
    except Exception as e:
        return jsonify({'message': f'Error fetching borrowings: {str(e)}'}), 500

@app.route('/borrowings', methods=['POST'])
@require_auth
def create_borrowing():
    """Create a new borrowing (for librarians/clerks)"""
    try:
        # Only librarians and clerks can create borrowings
        if request.user_role not in ['librarian', 'clerk', 'manager']:
            return jsonify({'message': 'Insufficient permissions'}), 403

        data = request.get_json()
        document_id = data.get('document_id')
        borrower_user_id = data.get('user_id', request.user_id)

        if not document_id:
            return jsonify({'message': 'Document ID is required'}), 400

        # Check if document exists and is available
        document_info = get_document_info(document_id)
        if not document_info:
            return jsonify({'message': 'Document not found'}), 404

        if document_info.get('available_copies', 0) <= 0:
            return jsonify({'message': 'Document is not available for borrowing'}), 400

        # Check if user is already borrowing this document
        existing_borrowing = Borrowing.query.filter_by(
            user_id=borrower_user_id,
            document_id=document_id,
            status='active'
        ).first()

        if existing_borrowing:
            return jsonify({'message': 'User is already borrowing this document'}), 400

        # Calculate due date (14 days from now)
        due_date = datetime.utcnow() + timedelta(days=14)

        # Create borrowing
        borrowing = Borrowing(
            user_id=borrower_user_id,
            document_id=document_id,
            due_date=due_date,
            notes=data.get('notes', '')
        )

        db.session.add(borrowing)

        # Update document availability
        if update_document_availability(document_id, 'borrow'):
            # Add to history
            history = MemberHistory(
                user_id=borrower_user_id,
                document_id=document_id,
                action='borrowed',
                details=f"Borrowed document: {document_info.get('title', 'Unknown')}"
            )
            db.session.add(history)

            # Check if user had a reservation for this document and fulfill it
            reservation = Reservation.query.filter_by(
                user_id=borrower_user_id,
                document_id=document_id,
                status='active'
            ).first()

            if reservation:
                reservation.status = 'fulfilled'
                reservation.updated_at = datetime.utcnow()

            db.session.commit()

            # Send notification
            send_notification(
                borrower_user_id,
                'borrowing_created',
                f"You have successfully borrowed '{document_info.get('title', 'Unknown')}'",
                document_id
            )

            return jsonify({
                'message': 'Borrowing created successfully',
                'borrowing': borrowing.to_dict()
            }), 201
        else:
            db.session.rollback()
            return jsonify({'message': 'Failed to update document availability'}), 500

    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Error creating borrowing: {str(e)}'}), 500

@app.route('/borrowings/<int:borrowing_id>/return', methods=['PUT'])
@require_auth
def return_document(borrowing_id):
    """Return a borrowed document"""
    try:
        # Only librarians and clerks can process returns
        if request.user_role not in ['librarian', 'clerk', 'manager']:
            return jsonify({'message': 'Insufficient permissions'}), 403

        borrowing = Borrowing.query.filter_by(id=borrowing_id, status='active').first()

        if not borrowing:
            return jsonify({'message': 'Active borrowing not found'}), 404

        # Update borrowing status
        borrowing.status = 'returned'
        borrowing.return_date = datetime.utcnow()
        borrowing.updated_at = datetime.utcnow()

        # Calculate fine if overdue
        if borrowing.due_date < datetime.utcnow():
            days_overdue = (datetime.utcnow() - borrowing.due_date).days
            fine_per_day = 0.50  # $0.50 per day
            borrowing.fine_amount = days_overdue * fine_per_day

        # Update document availability
        if update_document_availability(borrowing.document_id, 'return'):
            # Add to history
            document_info = get_document_info(borrowing.document_id)
            history = MemberHistory(
                user_id=borrowing.user_id,
                document_id=borrowing.document_id,
                action='returned',
                details=f"Returned document: {document_info.get('title', 'Unknown') if document_info else 'Unknown'}"
            )
            db.session.add(history)

            db.session.commit()

            # Send notification
            send_notification(
                borrowing.user_id,
                'document_returned',
                f"You have successfully returned '{document_info.get('title', 'Unknown') if document_info else 'Unknown'}'",
                borrowing.document_id
            )

            return jsonify({
                'message': 'Document returned successfully',
                'borrowing': borrowing.to_dict()
            }), 200
        else:
            db.session.rollback()
            return jsonify({'message': 'Failed to update document availability'}), 500

    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Error returning document: {str(e)}'}), 500

@app.route('/borrowings/<int:borrowing_id>/renew', methods=['PUT'])
@require_auth
def renew_borrowing(borrowing_id):
    """Renew a borrowing"""
    try:
        borrowing = Borrowing.query.filter_by(
            id=borrowing_id,
            user_id=request.user_id,
            status='active'
        ).first()

        if not borrowing:
            return jsonify({'message': 'Active borrowing not found'}), 404

        if borrowing.renewal_count >= borrowing.max_renewals:
            return jsonify({'message': 'Maximum renewals reached'}), 400

        # Check if document has pending reservations
        pending_reservations = Reservation.query.filter_by(
            document_id=borrowing.document_id,
            status='active'
        ).count()

        if pending_reservations > 0:
            return jsonify({'message': 'Cannot renew: document has pending reservations'}), 400

        # Extend due date by 14 days
        borrowing.due_date = borrowing.due_date + timedelta(days=14)
        borrowing.renewal_count += 1
        borrowing.updated_at = datetime.utcnow()

        # Add to history
        document_info = get_document_info(borrowing.document_id)
        history = MemberHistory(
            user_id=request.user_id,
            document_id=borrowing.document_id,
            action='renewed',
            details=f"Renewed document: {document_info.get('title', 'Unknown') if document_info else 'Unknown'}"
        )
        db.session.add(history)

        db.session.commit()

        # Send notification
        send_notification(
            request.user_id,
            'borrowing_renewed',
            f"You have successfully renewed '{document_info.get('title', 'Unknown') if document_info else 'Unknown'}'",
            borrowing.document_id
        )

        return jsonify({
            'message': 'Borrowing renewed successfully',
            'borrowing': borrowing.to_dict()
        }), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Error renewing borrowing: {str(e)}'}), 500

# History endpoints
@app.route('/history', methods=['GET'])
@require_auth
def get_user_history():
    """Get user's history"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        action_filter = request.args.get('action', '')

        query = MemberHistory.query.filter_by(user_id=request.user_id)

        if action_filter:
            query = query.filter_by(action=action_filter)

        history_pagination = query.order_by(MemberHistory.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        # Enrich with document information
        enriched_history = []
        for history_item in history_pagination.items:
            history_dict = history_item.to_dict()
            document_info = get_document_info(history_item.document_id)
            if document_info:
                history_dict['document'] = document_info
            enriched_history.append(history_dict)

        return jsonify({
            'history': enriched_history,
            'total': history_pagination.total,
            'pages': history_pagination.pages,
            'current_page': page,
            'per_page': per_page
        }), 200
    except Exception as e:
        return jsonify({'message': f'Error fetching history: {str(e)}'}), 500

# Dashboard summary endpoint
@app.route('/dashboard', methods=['GET'])
@require_auth
def get_dashboard_summary():
    """Get member dashboard summary"""
    try:
        # Get active reservations count
        active_reservations = Reservation.query.filter_by(
            user_id=request.user_id,
            status='active'
        ).count()

        # Get active borrowings count
        active_borrowings = Borrowing.query.filter_by(
            user_id=request.user_id,
            status='active'
        ).count()

        # Get overdue borrowings count
        overdue_borrowings = Borrowing.query.filter(
            Borrowing.user_id == request.user_id,
            Borrowing.status == 'active',
            Borrowing.due_date < datetime.utcnow()
        ).count()

        # Get total fines
        total_fines = db.session.query(db.func.sum(Borrowing.fine_amount)).filter_by(
            user_id=request.user_id
        ).scalar() or 0

        # Get recent history (last 5 items)
        recent_history = MemberHistory.query.filter_by(
            user_id=request.user_id
        ).order_by(MemberHistory.created_at.desc()).limit(5).all()

        # Enrich recent history with document info
        enriched_recent_history = []
        for history_item in recent_history:
            history_dict = history_item.to_dict()
            document_info = get_document_info(history_item.document_id)
            if document_info:
                history_dict['document'] = document_info
            enriched_recent_history.append(history_dict)

        return jsonify({
            'summary': {
                'active_reservations': active_reservations,
                'active_borrowings': active_borrowings,
                'overdue_borrowings': overdue_borrowings,
                'total_fines': float(total_fines)
            },
            'recent_history': enriched_recent_history
        }), 200
    except Exception as e:
        return jsonify({'message': f'Error fetching dashboard summary: {str(e)}'}), 500

# Admin endpoints (for librarians/managers)
@app.route('/admin/borrowings', methods=['GET'])
@require_auth
def get_all_borrowings():
    """Get all borrowings (admin only)"""
    try:
        if request.user_role not in ['librarian', 'manager']:
            return jsonify({'message': 'Insufficient permissions'}), 403

        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        status_filter = request.args.get('status', '')

        query = Borrowing.query

        if status_filter:
            query = query.filter_by(status=status_filter)

        borrowings_pagination = query.order_by(Borrowing.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        # Enrich with document information
        enriched_borrowings = []
        for borrowing in borrowings_pagination.items:
            borrowing_dict = borrowing.to_dict()
            document_info = get_document_info(borrowing.document_id)
            if document_info:
                borrowing_dict['document'] = document_info
            enriched_borrowings.append(borrowing_dict)

        return jsonify({
            'borrowings': enriched_borrowings,
            'total': borrowings_pagination.total,
            'pages': borrowings_pagination.pages,
            'current_page': page,
            'per_page': per_page
        }), 200
    except Exception as e:
        return jsonify({'message': f'Error fetching borrowings: {str(e)}'}), 500

@app.route('/admin/overdue', methods=['GET'])
@require_auth
def get_overdue_borrowings():
    """Get overdue borrowings (admin only)"""
    try:
        if request.user_role not in ['librarian', 'manager']:
            return jsonify({'message': 'Insufficient permissions'}), 403

        overdue_borrowings = Borrowing.query.filter(
            Borrowing.status == 'active',
            Borrowing.due_date < datetime.utcnow()
        ).order_by(Borrowing.due_date.asc()).all()

        # Enrich with document information
        enriched_borrowings = []
        for borrowing in overdue_borrowings:
            borrowing_dict = borrowing.to_dict()
            document_info = get_document_info(borrowing.document_id)
            if document_info:
                borrowing_dict['document'] = document_info

            # Calculate days overdue
            days_overdue = (datetime.utcnow() - borrowing.due_date).days
            borrowing_dict['days_overdue'] = days_overdue

            enriched_borrowings.append(borrowing_dict)

        return jsonify({
            'overdue_borrowings': enriched_borrowings,
            'total': len(enriched_borrowings)
        }), 200
    except Exception as e:
        return jsonify({'message': f'Error fetching overdue borrowings: {str(e)}'}), 500

# AI-powered features
@app.route('/ai/recommendations', methods=['GET'])
@require_auth
def get_ai_recommendations():
    """Get AI-powered book recommendations for the user"""
    try:
        from ai_recommendations import get_personalized_recommendations

        user_id = request.user_id
        limit = request.args.get('limit', 5, type=int)

        result = get_personalized_recommendations(user_id, limit)

        if result['success']:
            return jsonify({
                'message': 'Recommendations generated successfully',
                'recommendations': result['recommendations'],
                'preferences': result['preferences']
            }), 200
        else:
            return jsonify({
                'message': 'Failed to generate recommendations',
                'error': result.get('error', 'Unknown error')
            }), 500

    except Exception as e:
        return jsonify({'message': f'Error getting recommendations: {str(e)}'}), 500

@app.route('/ai/trending', methods=['GET'])
@require_auth
def get_trending_books():
    """Get trending books based on borrowing patterns"""
    try:
        from ai_recommendations import get_trending_books

        trending = get_trending_books()

        return jsonify({
            'message': 'Trending books retrieved successfully',
            'trending_books': trending
        }), 200

    except Exception as e:
        return jsonify({'message': f'Error getting trending books: {str(e)}'}), 500

@app.route('/ai/chat', methods=['POST'])
@require_auth
def chat_with_ai():
    """Chat with AI library assistant"""
    try:
        from ai_chat_assistant import chat_with_library_assistant

        data = request.get_json()
        if not data or 'message' not in data:
            return jsonify({'message': 'Message is required'}), 400

        user_id = request.user_id
        message = data['message']
        conversation_history = data.get('conversation_history', [])

        # Get auth token from request headers
        auth_header = request.headers.get('Authorization')
        auth_token = None
        if auth_header and auth_header.startswith('Bearer '):
            auth_token = auth_header.split(' ')[1]

        result = chat_with_library_assistant(user_id, message, conversation_history, auth_token)

        return jsonify({
            'success': result['success'],
            'response': result['response'],
            'context': result.get('context', {}),
            'timestamp': result['timestamp']
        }), 200

    except Exception as e:
        return jsonify({'message': f'Error in AI chat: {str(e)}'}), 500

@app.route('/ai/smart-search', methods=['GET'])
@require_auth
def ai_smart_search():
    """AI-powered smart search for books and documents"""
    try:
        from ai_chat_assistant import LibraryAIChatAssistant

        query = request.args.get('q', '')
        if not query:
            return jsonify({'message': 'Search query is required'}), 400

        # Get auth token from request headers
        auth_header = request.headers.get('Authorization')
        auth_token = None
        if auth_header and auth_header.startswith('Bearer '):
            auth_token = auth_header.split(' ')[1]

        # Use AI assistant to perform smart search
        assistant = LibraryAIChatAssistant()
        search_results = assistant.search_books(query, auth_token)

        return jsonify({
            'message': 'Smart search completed successfully',
            'query': query,
            'results': search_results,
            'total': len(search_results)
        }), 200

    except Exception as e:
        return jsonify({'message': f'Error in smart search: {str(e)}'}), 500

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        print("Member Service database tables created successfully!")

    app.run(debug=True, host='0.0.0.0', port=5006)
